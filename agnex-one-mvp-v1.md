# AgneX One - MVP v1.0 Foundation

## Version Overview
**Timeline**: 4 weeks  
**Focus**: Core authentication, basic role management, and simple dashboards  
**Goal**: Prove concept and establish foundation for role-based access system

## Core Features

### 🔐 Authentication System
- Firebase Auth integration
- Email/password authentication
- User registration with email verification
- Password reset functionality
- Session management

### 👥 Role Management (3 Roles Only)
- **Admin**: Full system access and user management
- **Client**: View-only access to assigned data
- **Support**: Basic ticket management

### 🏠 Basic Dashboards
- Role-specific landing pages
- Simple navigation based on user role
- Basic user profile management
- System status indicators

### 🎫 Simple Ticket System
- Create new tickets (Support and Client roles)
- View ticket list with basic filtering
- Simple status tracking (Open, In Progress, Closed)
- Basic ticket details view

### ⚙️ User Management (Admin Only)
- Add new users
- Assign roles to users
- View user list
- Basic user profile editing

## Technical Implementation

### Frontend Structure
```
src/
├── app/
│   ├── (auth)/
│   │   ├── login/page.tsx
│   │   ├── register/page.tsx
│   │   └── layout.tsx
│   ├── (dashboard)/
│   │   ├── admin/page.tsx
│   │   ├── client/page.tsx
│   │   ├── support/page.tsx
│   │   └── layout.tsx
│   ├── tickets/
│   │   ├── page.tsx
│   │   ├── new/page.tsx
│   │   └── [id]/page.tsx
│   └── users/
│       └── page.tsx (admin only)
├── components/
│   ├── ui/ (shadcn components)
│   ├── auth/
│   │   ├── LoginForm.tsx
│   │   ├── RegisterForm.tsx
│   │   └── AuthGuard.tsx
│   ├── dashboard/
│   │   ├── AdminDashboard.tsx
│   │   ├── ClientDashboard.tsx
│   │   └── SupportDashboard.tsx
│   ├── tickets/
│   │   ├── TicketList.tsx
│   │   ├── TicketForm.tsx
│   │   └── TicketCard.tsx
│   └── users/
│       ├── UserList.tsx
│       └── UserForm.tsx
├── lib/
│   ├── firebase.ts
│   ├── auth.ts
│   └── utils.ts
└── hooks/
    ├── useAuth.ts
    └── useRole.ts
```

### Database Schema (Firestore)

#### Users Collection
```javascript
users/{userId} {
  email: string,
  role: 'admin' | 'client' | 'support',
  displayName: string,
  createdAt: timestamp,
  lastLogin: timestamp,
  isActive: boolean
}
```

#### Tickets Collection
```javascript
tickets/{ticketId} {
  title: string,
  description: string,
  status: 'open' | 'in_progress' | 'closed',
  priority: 'low' | 'medium' | 'high',
  createdBy: string, // userId
  assignedTo: string, // userId (optional)
  createdAt: timestamp,
  updatedAt: timestamp,
  clientId: string // for client-specific tickets
}
```

### Custom Claims Structure
```javascript
{
  role: 'admin' | 'client' | 'support',
  permissions: {
    canManageUsers: boolean,
    canViewAllTickets: boolean,
    canCreateTickets: boolean,
    canAssignTickets: boolean
  }
}
```

### Cloud Functions (Basic)
```javascript
// functions/src/index.ts
exports.setUserRole = functions.https.onCall(async (data, context) => {
  // Set custom claims for user role
});

exports.createTicket = functions.firestore
  .document('tickets/{ticketId}')
  .onCreate(async (snap, context) => {
    // Send notification on ticket creation
  });
```

## Development Phases

### Week 1: Project Setup & Authentication
- [ ] Initialize Next.js project with TypeScript
- [ ] Setup Shadcn/ui and Tailwind CSS
- [ ] Configure Firebase project
- [ ] Implement authentication pages (login/register)
- [ ] Setup Firebase Auth with email/password
- [ ] Create basic layout and navigation

### Week 2: Role System & Dashboards
- [ ] Implement custom claims system
- [ ] Create role-based route protection
- [ ] Build basic dashboard layouts for each role
- [ ] Implement user context and role hooks
- [ ] Create navigation based on user roles
- [ ] Setup basic user profile management

### Week 3: Ticket System
- [ ] Design ticket data structure
- [ ] Build ticket creation form
- [ ] Implement ticket listing with basic filtering
- [ ] Create ticket detail view
- [ ] Add basic status management
- [ ] Implement role-based ticket access

### Week 4: User Management & Polish
- [ ] Build admin user management interface
- [ ] Implement user creation and role assignment
- [ ] Add basic error handling and loading states
- [ ] Implement responsive design
- [ ] Add basic notifications/toasts
- [ ] Testing and bug fixes

## UI Components (Shadcn/ui)

### Required Components
- Button, Input, Label, Card
- Form, Select, Textarea
- Table, Badge, Avatar
- Dialog, Alert, Toast
- Tabs, Separator

### Custom Components
- AuthGuard (route protection)
- RoleBasedComponent (conditional rendering)
- TicketStatusBadge
- UserRoleBadge
- DashboardCard

## Security Implementation

### Route Protection
```typescript
// middleware.ts
export function middleware(request: NextRequest) {
  // Check authentication and role-based access
}

// components/auth/AuthGuard.tsx
export function AuthGuard({ children, requiredRole }) {
  // Protect components based on user role
}
```

### Firebase Security Rules
```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /users/{userId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == userId || 
         request.auth.token.role == 'admin');
    }
    
    match /tickets/{ticketId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null && 
        request.auth.token.role in ['support', 'client'];
      allow update: if request.auth != null && 
        request.auth.token.role in ['admin', 'support'];
    }
  }
}
```

## Testing Strategy

### Unit Tests
- Authentication functions
- Role-based access logic
- Form validation
- Utility functions

### Integration Tests
- User registration flow
- Login/logout functionality
- Ticket creation and viewing
- Role-based navigation

### Manual Testing
- Cross-browser compatibility
- Mobile responsiveness
- User experience flows
- Security access controls

## Success Metrics

### Technical Metrics
- [ ] 100% authentication success rate
- [ ] Role-based access working correctly
- [ ] All CRUD operations functional
- [ ] Responsive design on mobile/desktop

### User Experience Metrics
- [ ] Intuitive navigation for all roles
- [ ] Clear feedback for user actions
- [ ] Fast page load times (<3s)
- [ ] Error handling with helpful messages

### Business Metrics
- [ ] Users can successfully create accounts
- [ ] Admins can manage users effectively
- [ ] Tickets can be created and tracked
- [ ] Role separation is clear and functional

## Deployment

### Environment Setup
- **Development**: Local Firebase emulator
- **Staging**: Firebase staging project
- **Production**: Firebase production project

### CI/CD Pipeline
- GitHub Actions for automated testing
- Automated deployment to staging
- Manual approval for production deployment

## Known Limitations (v1.0)

### Features Not Included
- HR and Finance roles (added in v2.0)
- Employee management
- Advanced reporting
- AI integration
- File attachments
- Advanced notifications

### Technical Limitations
- Basic UI styling
- Limited error handling
- No offline support
- Basic search functionality

## Next Steps to v2.0
- Add HR and Finance roles
- Implement employee management
- Enhanced ticket system with assignments
- Improved UI/UX design
- Advanced filtering and search

---

**MVP v1.0 Goal**: Establish a solid foundation with core authentication, basic role management, and simple ticket system that proves the concept and allows for user feedback.
